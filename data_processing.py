import pandas as pd
import numpy as np
from torch.utils.data import DataLoader, Dataset
import torch

class CustomTensorDataset(Dataset):
    def __init__(self, embeddings, labels):
        self.embeddings = embeddings
        self.labels = labels

    def __len__(self):
        return len(self.embeddings)

    def __getitem__(self, idx):
        embedding = torch.tensor(self.embeddings[idx])
        label = torch.tensor(self.labels[idx])
        return embedding, label
    
def normalize_labels(labels):
    
    labels = np.array(labels)
    for i in range(len(labels[0, :])):
        labels[:, i] = (labels[:, i] - np.mean(labels[:, i])) / np.std(labels[:, i])

    return labels.tolist()


def create_dataloaders(args):
    # conlumns includs microarchitecture embedding, performance, power, area, time of the VLSI flow
    dataset = pd.read_csv('./contest.csv')
    
    dataset = dataset.to_numpy()
    data = []
    for row in dataset:
        data.append(np.array((row[0]).split()).astype('float32'))

    # first 29 columns are embedding, use power and area as label
    embeddings = []
    labels = []

    for row in data:
        embeddings.append(row[:29].tolist())
        labels.append(row[30:32].tolist())

    labels = normalize_labels(labels)

    # splitting by kfold validation parameter
    train_data, train_labels = [], []
    test_data, test_labels = [], []

    dataset_length = len(embeddings)

    kfold = args.kfold
    # 0 to kfold - 1
    test_fold = args.test_fold
    test_data_start, test_data_end = dataset_length // kfold * test_fold, dataset_length // kfold * (test_fold + 1)

    dataset_flags = [0 if test_data_start <= i < test_data_end else 1 for i in range(dataset_length)]

    for embedding, label, flag in zip(embeddings, labels, dataset_flags):
        # data should be used in training dataset
        if flag:
            train_data.append(embedding)
            train_labels.append(label)
        else:
            test_data.append(embedding)
            test_labels.append(label)

    train_dataset = CustomTensorDataset(train_data, train_labels)
    test_dataset = CustomTensorDataset(test_data, test_labels)

    train_dataloader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=False)
    test_dataloader = DataLoader(test_dataset, batch_size=args.batch_size, shuffle=False)


    return train_dataloader, test_dataloader

# train_dataloader, test_dataloader = create_dataloaders()


# embeddings, train_labels = next(iter(train_dataloader))
# print(embeddings, train_labels, len(train_dataloader.dataset))