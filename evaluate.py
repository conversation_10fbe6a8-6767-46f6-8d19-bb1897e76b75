import torch
import numpy as np
from sklearn.metrics import mean_squared_error, r2_score
from arguments import get_args

def judge_correct(pred, label):
    args = get_args()
    diff = torch.abs(pred - label)
    power_correct = torch.where(torch.abs(diff[:, 0] / label[:, 0]) < args.epsilon_power, 1, 0).sum().item()
    area_correct = torch.where(torch.abs(diff[:, 1] / label[:, 1]) < args.epsilon_area , 1, 0).sum().item()
    # print("power", torch.exp(pred[:, 0]), label[:, 0])
    # print("area", torch.exp(pred[:, 1]), label[:, 1])
    return power_correct, area_correct


def test(dataloader, model, loss_fn, device='cpu'):

    size = len(dataloader.dataset)
    num_batches = len(dataloader)
    model.eval()
    test_loss = 0
    power_correct, area_correct = 0, 0
    with torch.no_grad():
        for embedding, label in dataloader:
            embedding, label = embedding.to(device), label.to(device)
            pred = model(embedding)
            correct_num = judge_correct(pred, label)
            power_correct += correct_num[0]
            area_correct += correct_num[1]
            test_loss += loss_fn(pred, label).item()
    test_loss /= num_batches
    power_correct /= size
    area_correct /= size
    print(f"Test average loss: {test_loss:>8f}, power tolerable pred num: {power_correct:>2f}, area tolerable pred num: {area_correct:>4f},  \n",)

    return test_loss, power_correct, area_correct


def test_with_metrics(dataloader, model, loss_fn, device='cpu'):
    """
    Enhanced test function that calculates MSE, R², and tolerance-based accuracy.

    Args:
        dataloader: Test data loader
        model: Model to evaluate
        loss_fn: Loss function
        device: Device to run evaluation on

    Returns:
        dict: Dictionary containing all metrics
    """
    size = len(dataloader.dataset)
    num_batches = len(dataloader)
    model.eval()
    test_loss = 0
    power_correct, area_correct = 0, 0

    # Collect all predictions and labels for R² calculation
    all_preds = []
    all_labels = []

    with torch.no_grad():
        for embedding, label in dataloader:
            embedding, label = embedding.to(device), label.to(device)
            pred = model(embedding)
            correct_num = judge_correct(pred, label)
            power_correct += correct_num[0]
            area_correct += correct_num[1]
            test_loss += loss_fn(pred, label).item()

            # Store predictions and labels for additional metrics
            all_preds.append(pred.cpu().numpy())
            all_labels.append(label.cpu().numpy())

    # Concatenate all predictions and labels
    all_preds = np.concatenate(all_preds, axis=0)
    all_labels = np.concatenate(all_labels, axis=0)

    # Calculate metrics
    test_loss /= num_batches
    power_correct /= size
    area_correct /= size

    # Calculate MSE for each target
    power_mse = mean_squared_error(all_labels[:, 0], all_preds[:, 0])
    area_mse = mean_squared_error(all_labels[:, 1], all_preds[:, 1])
    overall_mse = mean_squared_error(all_labels, all_preds)

    # Calculate R² for each target
    power_r2 = r2_score(all_labels[:, 0], all_preds[:, 0])
    area_r2 = r2_score(all_labels[:, 1], all_preds[:, 1])
    overall_r2 = r2_score(all_labels, all_preds)

    print(f"Test average loss: {test_loss:>8f}")
    print(f"Power - MSE: {power_mse:>8f}, R²: {power_r2:>6f}, Accuracy: {power_correct:>4f}")
    print(f"Area  - MSE: {area_mse:>8f}, R²: {area_r2:>6f}, Accuracy: {area_correct:>4f}")
    print(f"Overall - MSE: {overall_mse:>8f}, R²: {overall_r2:>6f}")

    return {
        'test_loss': test_loss,
        'power_correct': power_correct,
        'area_correct': area_correct,
        'power_mse': power_mse,
        'area_mse': area_mse,
        'overall_mse': overall_mse,
        'power_r2': power_r2,
        'area_r2': area_r2,
        'overall_r2': overall_r2
    }