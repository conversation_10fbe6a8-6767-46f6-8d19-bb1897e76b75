import numpy as np
import pandas as pd
import torch
from sklearn.linear_model import LinearRegression
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import RBF, Matern, RationalQuadratic
from sklearn.metrics import mean_squared_error, r2_score
from arguments import get_args


class SklearnWrapper:
    """
    PyTorch-compatible wrapper for scikit-learn regression models.
    This allows sklearn models to be used with the existing PyTorch training framework.
    """

    def __init__(self, model_type='linear_regression', **model_params):
        """
        Initialize sklearn wrapper with model type and hyperparameters.

        Args:
            model_type: Type of model ('linear_regression' or 'gaussian_process')
            **model_params: Model-specific hyperparameters
        """
        self.model_type = model_type
        self.model_params = model_params
        self.model_power = None  # Model for power prediction
        self.model_area = None   # Model for area prediction
        self.is_trained = False

    def train(self):
        """Set model to training mode (for compatibility with PyTorch)"""
        pass

    def eval(self):
        """Set model to evaluation mode (for compatibility with PyTorch)"""
        pass

    def to(self, device):
        """Move model to device (for compatibility with PyTorch)"""
        return self

    def _create_model(self):
        """Create a new instance of the specified model type."""
        if self.model_type == 'linear_regression':
            return LinearRegression(**self.model_params)
        elif self.model_type == 'gaussian_process':
            return GaussianProcessRegressor(**self.model_params)
        else:
            raise ValueError(f"Unsupported model type: {self.model_type}")

    def fit(self, X_train, y_train, X_val=None, y_val=None, verbose=True):
        """
        Train sklearn models for power and area prediction.

        Args:
            X_train: Training features (numpy array)
            y_train: Training labels (numpy array) - shape (n_samples, 2) for [power, area]
            X_val: Validation features (optional, not used for sklearn models)
            y_val: Validation labels (optional, not used for sklearn models)
            verbose: Whether to print training progress
        """
        if verbose:
            print(f"Training {self.model_type} models...")

        # Create separate models for power and area
        self.model_power = self._create_model()
        self.model_area = self._create_model()

        # Train power model
        if verbose:
            print("Training power prediction model...")
        self.model_power.fit(X_train, y_train[:, 0])

        # Train area model
        if verbose:
            print("Training area prediction model...")
        self.model_area.fit(X_train, y_train[:, 1])

        self.is_trained = True

        if verbose:
            print(f"{self.model_type} training completed!")

    def predict(self, X):
        """
        Make predictions using trained sklearn models.

        Args:
            X: Input features (numpy array or torch tensor)

        Returns:
            torch.Tensor: Predictions with shape (n_samples, 2) for [power, area]
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")

        # Convert torch tensor to numpy if needed
        if isinstance(X, torch.Tensor):
            X_np = X.detach().cpu().numpy()
        else:
            X_np = X

        # Make predictions
        power_pred = self.model_power.predict(X_np)
        area_pred = self.model_area.predict(X_np)

        # Stack predictions and convert to torch tensor
        predictions = np.column_stack([power_pred, area_pred])
        return torch.tensor(predictions, dtype=torch.float32)

    def __call__(self, X):
        """Make the wrapper callable like PyTorch models."""
        return self.predict(X)


def create_sklearn_data(args):
    """
    Create numpy arrays for sklearn training from the dataset.
    This is similar to create_dataloaders but returns numpy arrays instead of DataLoaders.

    Args:
        args: Command line arguments

    Returns:
        tuple: (X_train, y_train, X_test, y_test) as numpy arrays
    """
    # Load and process data (same as in data_processing.py)
    dataset = pd.read_csv('./contest.csv')
    dataset = dataset.to_numpy()

    data = []
    for row in dataset:
        data.append(np.array((row[0]).split()).astype('float32'))

    # Extract features and labels
    embeddings = []
    labels = []

    for row in data:
        embeddings.append(row[:29].tolist())
        labels.append(row[30:32].tolist())  # power and area

    # Note: We don't normalize labels for sklearn models as they handle it internally
    # Convert to numpy arrays
    X = np.array(embeddings)
    y = np.array(labels)

    # Split data using k-fold validation (same logic as in data_processing.py)
    dataset_length = len(X)
    kfold = args.kfold
    test_fold = args.test_fold
    test_data_start = dataset_length // kfold * test_fold
    test_data_end = dataset_length // kfold * (test_fold + 1)

    # Create train/test split
    test_indices = list(range(test_data_start, test_data_end))
    train_indices = list(range(0, test_data_start)) + list(range(test_data_end, dataset_length))

    X_train = X[train_indices]
    y_train = y[train_indices]
    X_test = X[test_indices]
    y_test = y[test_indices]

    return X_train, y_train, X_test, y_test


def create_linear_regression_model(args):
    """
    Create a Linear Regression model wrapper.

    Args:
        args: Command line arguments

    Returns:
        SklearnWrapper: Configured Linear Regression model
    """
    model_params = {
        'fit_intercept': True,
        'copy_X': True,
        'n_jobs': None,
        'positive': False
    }

    return SklearnWrapper(model_type='linear_regression', **model_params)


def create_gaussian_process_model(args):
    """
    Create a Gaussian Process model wrapper.

    Args:
        args: Command line arguments

    Returns:
        SklearnWrapper: Configured Gaussian Process model
    """
    # Create kernel based on args
    if args.gp_kernel == 'rbf':
        kernel = RBF(length_scale=1.0)
    elif args.gp_kernel == 'matern':
        kernel = Matern(length_scale=1.0, nu=1.5)
    elif args.gp_kernel == 'rational_quadratic':
        kernel = RationalQuadratic(length_scale=1.0, alpha=1.0)
    else:
        kernel = RBF(length_scale=1.0)  # Default fallback

    model_params = {
        'kernel': kernel,
        'alpha': args.gp_alpha,
        'normalize_y': args.gp_normalize_y,
        'copy_X_train': True,
        'random_state': 42
    }

    return SklearnWrapper(model_type='gaussian_process', **model_params)


def train_sklearn_model(X_train, y_train, X_test, y_test, model, args):
    """
    Train and evaluate a sklearn model.

    Args:
        X_train: Training features
        y_train: Training labels
        X_test: Test features
        y_test: Test labels
        model: SklearnWrapper model instance
        args: Command line arguments

    Returns:
        tuple: (train_losses, test_losses, test_acc) for compatibility with main.py
    """
    # Train the model
    model.fit(X_train, y_train, verbose=True)

    # Make predictions for evaluation
    train_pred = model.predict(X_train)
    test_pred = model.predict(X_test)

    # Calculate losses (MSE)
    train_loss = mean_squared_error(y_train, train_pred.numpy())
    test_loss = mean_squared_error(y_test, test_pred.numpy())

    # Calculate R² scores
    train_r2 = r2_score(y_train, train_pred.numpy())
    test_r2 = r2_score(y_test, test_pred.numpy())

    # Calculate accuracy using the same logic as in evaluate.py
    power_correct, area_correct = judge_correct_numpy(test_pred.numpy(), y_test, args)

    # Print metrics
    print(f"Train MSE: {train_loss:.6f}, Test MSE: {test_loss:.6f}")
    print(f"Train R²: {train_r2:.6f}, Test R²: {test_r2:.6f}")
    print(f"Power Accuracy: {power_correct:.4f}, Area Accuracy: {area_correct:.4f}")

    # Return single values as lists for compatibility with plotting functions
    train_losses = [train_loss]
    test_losses = [test_loss]
    test_acc = [[power_correct], [area_correct]]

    # Store additional metrics for later use
    model.train_r2 = train_r2
    model.test_r2 = test_r2

    return train_losses, test_losses, test_acc


def judge_correct_numpy(pred, label, args):
    """
    Calculate accuracy for sklearn predictions using numpy arrays.
    This is equivalent to judge_correct in evaluate.py but works with numpy arrays.

    Args:
        pred: Predictions (numpy array)
        label: True labels (numpy array)
        args: Command line arguments

    Returns:
        tuple: (power_correct, area_correct) accuracy ratios
    """
    diff = np.abs(pred - label)
    power_correct = np.sum(np.abs(diff[:, 0] / label[:, 0]) < args.epsilon_power) / len(label)
    area_correct = np.sum(np.abs(diff[:, 1] / label[:, 1]) < args.epsilon_area) / len(label)

    return power_correct, area_correct
