import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import pearsonr, spearmanr
import warnings
warnings.filterwarnings('ignore')

def load_and_parse_data(csv_path='./contest.csv'):
    """
    Load and parse the contest.csv file.
    Returns features (first 29 columns) and labels (power: column 30, area: column 31).
    """
    print("Loading dataset...")
    dataset = pd.read_csv(csv_path)
    
    # Convert to numpy array
    dataset = dataset.to_numpy()
    
    # Parse the data (assuming first column contains space-separated values)
    data = []
    for row in dataset:
        data.append(np.array((row[0]).split()).astype('float32'))
    
    data = np.array(data)
    
    # Extract features (first 29 columns) and labels (columns 30-31: power and area)
    features = data[:, :29]
    power = data[:, 30]  # Column 30 is power
    area = data[:, 31]   # Column 31 is area
    
    print(f"Dataset shape: {data.shape}")
    print(f"Features shape: {features.shape}")
    print(f"Power shape: {power.shape}")
    print(f"Area shape: {area.shape}")
    
    return features, power, area

def calculate_correlations(features, labels, label_name):
    """
    Calculate Pearson and Spearman correlations between features and labels.
    """
    n_features = features.shape[1]
    correlations = {
        'feature_index': [],
        'pearson_corr': [],
        'pearson_pvalue': [],
        'spearman_corr': [],
        'spearman_pvalue': []
    }
    
    print(f"\nCalculating correlations with {label_name}...")
    
    for i in range(n_features):
        feature = features[:, i]
        
        # Pearson correlation
        pearson_corr, pearson_p = pearsonr(feature, labels)
        
        # Spearman correlation
        spearman_corr, spearman_p = spearmanr(feature, labels)
        
        correlations['feature_index'].append(i)
        correlations['pearson_corr'].append(pearson_corr)
        correlations['pearson_pvalue'].append(pearson_p)
        correlations['spearman_corr'].append(spearman_corr)
        correlations['spearman_pvalue'].append(spearman_p)
    
    return pd.DataFrame(correlations)

def plot_correlations(corr_df, label_name, save_plots=True):
    """
    Create visualization plots for correlations.
    """
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(f'Feature Correlations with {label_name}', fontsize=16)
    
    # Pearson correlation bar plot
    axes[0, 0].bar(corr_df['feature_index'], corr_df['pearson_corr'])
    axes[0, 0].set_title('Pearson Correlation')
    axes[0, 0].set_xlabel('Feature Index')
    axes[0, 0].set_ylabel('Correlation Coefficient')
    axes[0, 0].axhline(y=0, color='r', linestyle='--', alpha=0.5)
    
    # Spearman correlation bar plot
    axes[0, 1].bar(corr_df['feature_index'], corr_df['spearman_corr'])
    axes[0, 1].set_title('Spearman Correlation')
    axes[0, 1].set_xlabel('Feature Index')
    axes[0, 1].set_ylabel('Correlation Coefficient')
    axes[0, 1].axhline(y=0, color='r', linestyle='--', alpha=0.5)
    
    # Absolute Pearson correlation (sorted)
    abs_pearson = corr_df['pearson_corr'].abs().sort_values(ascending=False)
    axes[1, 0].bar(range(len(abs_pearson)), abs_pearson.values)
    axes[1, 0].set_title('Absolute Pearson Correlation (Sorted)')
    axes[1, 0].set_xlabel('Feature Rank')
    axes[1, 0].set_ylabel('|Correlation Coefficient|')
    
    # Absolute Spearman correlation (sorted)
    abs_spearman = corr_df['spearman_corr'].abs().sort_values(ascending=False)
    axes[1, 1].bar(range(len(abs_spearman)), abs_spearman.values)
    axes[1, 1].set_title('Absolute Spearman Correlation (Sorted)')
    axes[1, 1].set_xlabel('Feature Rank')
    axes[1, 1].set_ylabel('|Correlation Coefficient|')
    
    plt.tight_layout()
    
    if save_plots:
        plt.savefig(f'correlation_analysis_{label_name.lower()}.png', dpi=300, bbox_inches='tight')
        print(f"Plot saved as 'correlation_analysis_{label_name.lower()}.png'")
    
    plt.show()

def print_top_correlations(corr_df, label_name, top_n=10):
    """
    Print top correlated features.
    """
    print(f"\n{'='*50}")
    print(f"TOP {top_n} FEATURES CORRELATED WITH {label_name.upper()}")
    print(f"{'='*50}")
    
    # Sort by absolute Pearson correlation
    pearson_sorted = corr_df.reindex(corr_df['pearson_corr'].abs().sort_values(ascending=False).index)
    print(f"\nTop {top_n} by Absolute Pearson Correlation:")
    print("-" * 70)
    print(f"{'Rank':<5} {'Feature':<10} {'Pearson':<12} {'P-value':<12} {'Spearman':<12}")
    print("-" * 70)
    
    for i, (_, row) in enumerate(pearson_sorted.head(top_n).iterrows()):
        print(f"{i+1:<5} {int(row['feature_index']):<10} {row['pearson_corr']:<12.4f} "
              f"{row['pearson_pvalue']:<12.2e} {row['spearman_corr']:<12.4f}")
    
    # Sort by absolute Spearman correlation
    spearman_sorted = corr_df.reindex(corr_df['spearman_corr'].abs().sort_values(ascending=False).index)
    print(f"\nTop {top_n} by Absolute Spearman Correlation:")
    print("-" * 70)
    print(f"{'Rank':<5} {'Feature':<10} {'Spearman':<12} {'P-value':<12} {'Pearson':<12}")
    print("-" * 70)
    
    for i, (_, row) in enumerate(spearman_sorted.head(top_n).iterrows()):
        print(f"{i+1:<5} {int(row['feature_index']):<10} {row['spearman_corr']:<12.4f} "
              f"{row['spearman_pvalue']:<12.2e} {row['pearson_corr']:<12.4f}")

def save_correlation_results(power_corr_df, area_corr_df):
    """
    Save correlation results to CSV files.
    """
    power_corr_df.to_csv('power_correlations.csv', index=False)
    area_corr_df.to_csv('area_correlations.csv', index=False)
    print(f"\nCorrelation results saved to:")
    print(f"- power_correlations.csv")
    print(f"- area_correlations.csv")

def main():
    """
    Main function to run the correlation analysis.
    """
    print("Starting Feature Correlation Analysis")
    print("=" * 50)
    
    # Load and parse data
    features, power, area = load_and_parse_data()
    
    # Calculate correlations for power
    power_correlations = calculate_correlations(features, power, "Power")
    
    # Calculate correlations for area
    area_correlations = calculate_correlations(features, area, "Area")
    
    # Print top correlations
    print_top_correlations(power_correlations, "Power")
    print_top_correlations(area_correlations, "Area")
    
    # Create and save plots
    plot_correlations(power_correlations, "Power")
    plot_correlations(area_correlations, "Area")
    
    # Save results to CSV
    save_correlation_results(power_correlations, area_correlations)
    
    # Summary statistics
    print(f"\n{'='*50}")
    print("SUMMARY STATISTICS")
    print(f"{'='*50}")
    
    print(f"\nPower Correlations:")
    print(f"Max absolute Pearson correlation: {power_correlations['pearson_corr'].abs().max():.4f}")
    print(f"Max absolute Spearman correlation: {power_correlations['spearman_corr'].abs().max():.4f}")
    print(f"Mean absolute Pearson correlation: {power_correlations['pearson_corr'].abs().mean():.4f}")
    print(f"Mean absolute Spearman correlation: {power_correlations['spearman_corr'].abs().mean():.4f}")
    
    print(f"\nArea Correlations:")
    print(f"Max absolute Pearson correlation: {area_correlations['pearson_corr'].abs().max():.4f}")
    print(f"Max absolute Spearman correlation: {area_correlations['spearman_corr'].abs().max():.4f}")
    print(f"Mean absolute Pearson correlation: {area_correlations['pearson_corr'].abs().mean():.4f}")
    print(f"Mean absolute Spearman correlation: {area_correlations['spearman_corr'].abs().mean():.4f}")

if __name__ == "__main__":
    main()
