

def train(dataloader, model, loss_fn, optimizer, scheduler=None, device='cpu'):
    size = len(dataloader.dataset)
    num_batches = len(dataloader)
    model.train()
    for batch_id, (embedding, label) in enumerate(dataloader):
        embedding, label = embedding.to(device), label.to(device)

        # Compute prediction error
        pred = model(embedding)
        loss = loss_fn(pred, label)

        # Backpropagation
        loss.backward()
        optimizer.step()
        optimizer.zero_grad()
        if scheduler is not None:
            scheduler.step()

        if batch_id % 1000 == 0:
            loss, current = loss.item(), (batch_id + 1) * len(embedding)
            print(f"Training loss: {loss:>7f}, at epoch [{current:>5d}/{size:>5d}]")
    
    return loss.item()