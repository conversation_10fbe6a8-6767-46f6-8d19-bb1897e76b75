#!/usr/bin/env python3
"""
Run XGBoost tests with different numbers of top correlated features
using the existing framework infrastructure.
"""

import subprocess
import pandas as pd
import numpy as np
from scipy.stats import pearsonr
import matplotlib.pyplot as plt
import os
import sys

def load_and_analyze_correlations(csv_path='./contest.csv'):
    """
    Load data and calculate feature correlations to identify top features.
    Returns the indices of top features for different counts.
    """
    print("Analyzing feature correlations...")
    
    # Load and parse data (same logic as data_processing.py)
    dataset = pd.read_csv(csv_path)
    dataset = dataset.to_numpy()
    
    data = []
    for row in dataset:
        data.append(np.array((row[0]).split()).astype('float32'))
    
    data = np.array(data)
    
    # Extract features and labels
    features = data[:, :29]
    power = data[:, 30]
    area = data[:, 31]
    
    # Calculate correlations
    n_features = features.shape[1]
    correlations = []
    
    for i in range(n_features):
        feature = features[:, i]
        power_corr, _ = pearsonr(feature, power)
        area_corr, _ = pearsonr(feature, area)
        avg_abs_corr = (abs(power_corr) + abs(area_corr)) / 2
        
        correlations.append({
            'feature_index': i,
            'power_corr': power_corr,
            'area_corr': area_corr,
            'avg_abs_corr': avg_abs_corr
        })
    
    corr_df = pd.DataFrame(correlations)
    corr_df = corr_df.sort_values('avg_abs_corr', ascending=False)
    
    # Get top feature indices for different counts
    top_features = {}
    for n in [5, 10, 15, 20]:
        if n <= len(corr_df):
            top_features[n] = corr_df.head(n)['feature_index'].values
    
    print("Top 10 features by average absolute correlation:")
    print(corr_df.head(10)[['feature_index', 'power_corr', 'area_corr', 'avg_abs_corr']])
    
    return top_features, corr_df

def create_modified_xgboost_utils(selected_features, output_file='xgboost_utils_modified.py'):
    """
    Create a modified version of xgboost_utils.py that uses only selected features.
    """
    # Read the original xgboost_utils.py
    with open('xgboost_utils.py', 'r') as f:
        content = f.read()
    
    # Create the feature selection code
    feature_selection_code = f"""
# FEATURE SELECTION: Using only features {list(selected_features)}
def apply_feature_selection(X):
    \"\"\"Apply feature selection to keep only selected features.\"\"\"
    selected_indices = {list(selected_features)}
    return X[:, selected_indices]
"""
    
    # Modify the create_xgboost_data function to apply feature selection
    modified_content = content.replace(
        "def create_xgboost_data(args):",
        feature_selection_code + "\ndef create_xgboost_data(args):"
    )
    
    # Add feature selection to the data processing
    modified_content = modified_content.replace(
        "X_train = np.array(train_data)",
        "X_train = apply_feature_selection(np.array(train_data))"
    )
    
    modified_content = modified_content.replace(
        "X_test = np.array(test_data)",
        "X_test = apply_feature_selection(np.array(test_data))"
    )
    
    # Write the modified file
    with open(output_file, 'w') as f:
        f.write(modified_content)
    
    print(f"Created modified XGBoost utils: {output_file}")

def run_xgboost_test(n_features, selected_features, test_fold=3):
    """
    Run XGBoost test with selected features.
    """
    print(f"\n{'='*60}")
    print(f"Testing XGBoost with top {n_features} features")
    print(f"Selected features: {selected_features}")
    print(f"{'='*60}")
    
    # Create modified xgboost_utils.py
    modified_utils_file = f'xgboost_utils_top{n_features}.py'
    create_modified_xgboost_utils(selected_features, modified_utils_file)
    
    # Create a temporary main script that uses the modified utils
    temp_main_content = f"""
import sys
sys.path.insert(0, '.')

# Import modified xgboost_utils
import importlib.util
spec = importlib.util.spec_from_file_location("xgboost_utils_modified", "{modified_utils_file}")
xgboost_utils_modified = importlib.util.module_from_spec(spec)
spec.loader.exec_module(xgboost_utils_modified)

# Replace the original imports
import xgboost_utils
xgboost_utils.create_xgboost_data = xgboost_utils_modified.create_xgboost_data

# Now run the original main.py logic
from train import train
from evaluate import test
from data_processing import create_dataloaders
from arguments import parse_args
from model import *
from plotting import *
import torch.nn as nn
import torch
import pandas as pd

if __name__ == '__main__':
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # Parse arguments and set model to xgboost
    args = parse_args()
    args.model_name = 'xgboost'
    args.test_fold = {test_fold}
    
    print("Using XGBoost model with feature selection")
    # Use XGBoost-specific data processing (with feature selection)
    X_train, y_train, X_test, y_test = xgboost_utils_modified.create_xgboost_data(args)
    model = xgboost_utils_modified.create_xgboost_model(args)
    
    # Train XGBoost model
    train_losses, test_losses, test_acc = xgboost_utils_modified.train_xgboost(X_train, y_train, X_test, y_test, model, args)
    epochs = 1  # XGBoost trains in one go
    
    # Plot results with modified names
    args.model_name = f'xgboost_top{n_features}'
    plot_losses(train_losses, test_losses, epochs, args)
    plot_test_acc(test_acc, epochs, args)
    
    # Save results
    res = []
    res.append(train_losses)
    res.append(test_losses)
    res.append(test_acc[0])
    res.append(test_acc[1])
    
    pd.DataFrame(res, index=['train losses', 'test losses', 'power acc', 'area acc']).to_csv(f'./res/{{args.model_name}}-{{args.test_fold}}.csv')
    
    print(f"Results saved for top {n_features} features")
    print(f"Train Loss: {{train_losses[0]:.4f}}")
    print(f"Test Loss: {{test_losses[0]:.4f}}")
    print(f"Power Accuracy: {{test_acc[0][0]:.4f}}")
    print(f"Area Accuracy: {{test_acc[1][0]:.4f}}")
"""
    
    temp_main_file = f'temp_main_top{n_features}.py'
    with open(temp_main_file, 'w') as f:
        f.write(temp_main_content)
    
    # Run the test
    try:
        result = subprocess.run([sys.executable, temp_main_file], 
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✓ Test completed successfully")
            print(result.stdout)
        else:
            print("✗ Test failed")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            
    except subprocess.TimeoutExpired:
        print("✗ Test timed out")
    except Exception as e:
        print(f"✗ Error running test: {e}")
    
    # Clean up temporary files
    try:
        os.remove(temp_main_file)
        os.remove(modified_utils_file)
    except:
        pass

def collect_and_analyze_results():
    """
    Collect results from all tests and create comparison analysis.
    """
    print(f"\n{'='*60}")
    print("COLLECTING AND ANALYZING RESULTS")
    print(f"{'='*60}")
    
    results = []
    feature_counts = [5, 10, 15, 20]
    
    for n_features in feature_counts:
        result_file = f'./res/xgboost_top{n_features}-3.csv'
        if os.path.exists(result_file):
            df = pd.read_csv(result_file, index_col=0)
            
            results.append({
                'n_features': n_features,
                'train_loss': df.loc['train losses'].iloc[0],
                'test_loss': df.loc['test losses'].iloc[0],
                'power_acc': df.loc['power acc'].iloc[0],
                'area_acc': df.loc['area acc'].iloc[0]
            })
        else:
            print(f"Warning: Result file not found for {n_features} features")
    
    if results:
        results_df = pd.DataFrame(results)
        print("\nComparison of XGBoost Performance with Different Feature Counts:")
        print(results_df.to_string(index=False, float_format='%.4f'))
        
        # Save comparison results
        results_df.to_csv('xgboost_feature_comparison.csv', index=False)
        print(f"\nComparison results saved to 'xgboost_feature_comparison.csv'")
        
        # Create comparison plots
        create_comparison_plots(results_df)
        
        # Find best configuration
        best_test_loss = results_df.loc[results_df['test_loss'].idxmin()]
        best_power_acc = results_df.loc[results_df['power_acc'].idxmax()]
        best_area_acc = results_df.loc[results_df['area_acc'].idxmax()]
        
        print(f"\nBest Test Loss: {int(best_test_loss['n_features'])} features (Loss: {best_test_loss['test_loss']:.4f})")
        print(f"Best Power Accuracy: {int(best_power_acc['n_features'])} features (Acc: {best_power_acc['power_acc']:.4f})")
        print(f"Best Area Accuracy: {int(best_area_acc['n_features'])} features (Acc: {best_area_acc['area_acc']:.4f})")
    
    else:
        print("No results found to analyze")

def create_comparison_plots(results_df):
    """Create comparison plots for different feature counts."""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('XGBoost Performance vs Number of Top Features', fontsize=16)
    
    # Test Loss
    axes[0, 0].plot(results_df['n_features'], results_df['test_loss'], 'bo-', linewidth=2, markersize=8)
    axes[0, 0].set_title('Test Loss vs Number of Features')
    axes[0, 0].set_xlabel('Number of Features')
    axes[0, 0].set_ylabel('Test Loss (MSE)')
    axes[0, 0].grid(True, alpha=0.3)
    
    # Train Loss
    axes[0, 1].plot(results_df['n_features'], results_df['train_loss'], 'go-', linewidth=2, markersize=8)
    axes[0, 1].set_title('Train Loss vs Number of Features')
    axes[0, 1].set_xlabel('Number of Features')
    axes[0, 1].set_ylabel('Train Loss (MSE)')
    axes[0, 1].grid(True, alpha=0.3)
    
    # Power Accuracy
    axes[1, 0].plot(results_df['n_features'], results_df['power_acc'], 'ro-', linewidth=2, markersize=8)
    axes[1, 0].set_title('Power Prediction Accuracy vs Number of Features')
    axes[1, 0].set_xlabel('Number of Features')
    axes[1, 0].set_ylabel('Power Accuracy')
    axes[1, 0].grid(True, alpha=0.3)
    
    # Area Accuracy
    axes[1, 1].plot(results_df['n_features'], results_df['area_acc'], 'mo-', linewidth=2, markersize=8)
    axes[1, 1].set_title('Area Prediction Accuracy vs Number of Features')
    axes[1, 1].set_xlabel('Number of Features')
    axes[1, 1].set_ylabel('Area Accuracy')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('xgboost_feature_comparison_plots.png', dpi=300, bbox_inches='tight')
    print(f"Comparison plots saved as 'xgboost_feature_comparison_plots.png'")
    plt.show()

def main():
    """Main function to run all XGBoost feature selection tests."""
    print("XGBoost Feature Selection Testing")
    print("=" * 60)
    
    # Analyze correlations and get top features
    top_features, corr_df = load_and_analyze_correlations()
    
    # Save correlation analysis
    corr_df.to_csv('feature_correlations_analysis.csv', index=False)
    print(f"Feature correlation analysis saved to 'feature_correlations_analysis.csv'")
    
    # Run tests for different feature counts
    for n_features, selected_features in top_features.items():
        run_xgboost_test(n_features, selected_features)
    
    # Collect and analyze all results
    collect_and_analyze_results()
    
    print(f"\n{'='*60}")
    print("ALL TESTS COMPLETED")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
