import numpy as np
import pandas as pd
import xgboost as xgb
import torch
from sklearn.metrics import mean_squared_error
from arguments import get_args


class XGBoostWrapper:
    """
    PyTorch-compatible wrapper for XGBoost regression model.
    This allows XGBoost to be used with the existing PyTorch training framework.
    """

    def __init__(self, **xgb_params):
        """
        Initialize XGBoost wrapper with hyperparameters.

        Args:
            **xgb_params: XGBoost hyperparameters
        """
        self.xgb_params = xgb_params
        self.model_power = None  # XGBoost model for power prediction
        self.model_area = None   # XGBoost model for area prediction
        self.is_trained = False

    def train(self):
        """Set model to training mode (for compatibility with PyTorch)"""
        pass

    def eval(self):
        """Set model to evaluation mode (for compatibility with PyTorch)"""
        pass

    def to(self, device):
        """Move model to device (for compatibility with PyTorch)"""
        return self

    def fit(self, X_train, y_train, X_val=None, y_val=None, verbose=True):
        """
        Train XGBoost models for power and area prediction.

        Args:
            X_train: Training features (numpy array)
            y_train: Training labels (numpy array) - shape (n_samples, 2) for [power, area]
            X_val: Validation features (optional)
            y_val: Validation labels (optional)
            verbose: Whether to print training progress
        """
        # Prepare training data
        dtrain_power = xgb.DMatrix(X_train, label=y_train[:, 0])  # Power labels
        dtrain_area = xgb.DMatrix(X_train, label=y_train[:, 1])   # Area labels

        # Prepare validation data if provided
        eval_list_power = [(dtrain_power, 'train')]
        eval_list_area = [(dtrain_area, 'train')]

        if X_val is not None and y_val is not None:
            dval_power = xgb.DMatrix(X_val, label=y_val[:, 0])
            dval_area = xgb.DMatrix(X_val, label=y_val[:, 1])
            eval_list_power.append((dval_power, 'val'))
            eval_list_area.append((dval_area, 'val'))

        # Extract n_estimators from params for num_boost_round
        num_boost_round = self.xgb_params.pop('n_estimators', 100)

        # Train power prediction model
        if verbose:
            print("Training XGBoost model for power prediction...")
        self.model_power = xgb.train(
            params=self.xgb_params,
            dtrain=dtrain_power,
            num_boost_round=num_boost_round,
            evals=eval_list_power,
            verbose_eval=verbose
        )

        # Train area prediction model
        if verbose:
            print("Training XGBoost model for area prediction...")
        self.model_area = xgb.train(
            params=self.xgb_params,
            dtrain=dtrain_area,
            num_boost_round=num_boost_round,
            evals=eval_list_area,
            verbose_eval=verbose
        )

        self.is_trained = True

    def predict(self, X):
        """
        Make predictions using trained XGBoost models.

        Args:
            X: Input features (numpy array or torch tensor)

        Returns:
            torch.Tensor: Predictions with shape (n_samples, 2) for [power, area]
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")

        # Convert torch tensor to numpy if needed
        if isinstance(X, torch.Tensor):
            X_np = X.detach().cpu().numpy()
        else:
            X_np = X

        # Create DMatrix for prediction
        dtest = xgb.DMatrix(X_np)

        # Make predictions
        power_pred = self.model_power.predict(dtest)
        area_pred = self.model_area.predict(dtest)

        # Stack predictions and convert to torch tensor
        predictions = np.column_stack([power_pred, area_pred])
        return torch.tensor(predictions, dtype=torch.float32)

    def __call__(self, X):
        """Make the wrapper callable like a PyTorch model"""
        return self.predict(X)


def create_xgboost_data(args):
    """
    Create numpy arrays for XGBoost training from the dataset.
    This is similar to create_dataloaders but returns numpy arrays instead of DataLoaders.

    Args:
        args: Command line arguments

    Returns:
        tuple: (X_train, y_train, X_test, y_test) as numpy arrays
    """
    # Load and process data (same as in data_processing.py)
    dataset = pd.read_csv('./contest.csv')
    dataset = dataset.to_numpy()

    data = []
    for row in dataset:
        data.append(np.array((row[0]).split()).astype('float32'))

    # Extract features and labels
    embeddings = []
    labels = []

    for row in data:
        embeddings.append(row[:29].tolist())
        labels.append(row[30:32].tolist())  # power and area

    # Normalize labels (same as in data_processing.py)
    labels = np.array(labels)
    for i in range(labels.shape[1]):
        labels[:, i] = (labels[:, i] - np.mean(labels[:, i])) / np.std(labels[:, i])

    # Convert to numpy arrays
    X = np.array(embeddings)
    y = labels

    # Split data using k-fold validation (same logic as in data_processing.py)
    dataset_length = len(X)
    kfold = args.kfold
    test_fold = args.test_fold
    test_data_start = dataset_length // kfold * test_fold
    test_data_end = dataset_length // kfold * (test_fold + 1)

    # Create train/test split
    test_indices = list(range(test_data_start, test_data_end))
    train_indices = list(range(0, test_data_start)) + list(range(test_data_end, dataset_length))

    X_train = X[train_indices]
    y_train = y[train_indices]
    X_test = X[test_indices]
    y_test = y[test_indices]

    return X_train, y_train, X_test, y_test


def create_xgboost_model(args):
    """
    Create XGBoost model with hyperparameters from command line arguments.

    Args:
        args: Command line arguments

    Returns:
        XGBoostWrapper: Configured XGBoost model wrapper
    """
    xgb_params = {
        'objective': 'reg:squarederror',
        'n_estimators': args.xgb_n_estimators,
        'max_depth': args.xgb_max_depth,
        'learning_rate': args.xgb_learning_rate,
        'subsample': args.xgb_subsample,
        'colsample_bytree': args.xgb_colsample_bytree,
        'reg_alpha': args.xgb_reg_alpha,
        'reg_lambda': args.xgb_reg_lambda,
        'random_state': 42,
        'verbosity': 1
    }

    return XGBoostWrapper(**xgb_params)


def train_xgboost(X_train, y_train, X_test, y_test, model, args):
    """
    Train XGBoost model and return training history for compatibility with existing framework.

    Args:
        X_train: Training features
        y_train: Training labels
        X_test: Test features
        y_test: Test labels
        model: XGBoostWrapper instance
        args: Command line arguments

    Returns:
        tuple: (train_losses, test_losses, test_acc) for compatibility with main.py
    """
    # Train the model
    model.fit(X_train, y_train, X_test, y_test, verbose=True)

    # Make predictions for evaluation
    train_pred = model.predict(X_train)
    test_pred = model.predict(X_test)

    # Calculate losses (MSE)
    train_loss = mean_squared_error(y_train, train_pred.numpy())
    test_loss = mean_squared_error(y_test, test_pred.numpy())

    # Calculate accuracy using the same logic as in evaluate.py
    power_correct, area_correct = judge_correct_numpy(test_pred.numpy(), y_test, args)

    # Return single values as lists for compatibility with plotting functions
    train_losses = [train_loss]
    test_losses = [test_loss]
    test_acc = [[power_correct], [area_correct]]

    return train_losses, test_losses, test_acc


def judge_correct_numpy(pred, label, args):
    """
    Calculate accuracy for XGBoost predictions using numpy arrays.
    This is equivalent to judge_correct in evaluate.py but works with numpy arrays.

    Args:
        pred: Predictions (numpy array)
        label: True labels (numpy array)
        args: Command line arguments

    Returns:
        tuple: (power_correct, area_correct) accuracy ratios
    """
    diff = np.abs(pred - label)
    power_correct = np.sum(np.abs(diff[:, 0] / label[:, 0]) < args.epsilon_power) / len(label)
    area_correct = np.sum(np.abs(diff[:, 1] / label[:, 1]) < args.epsilon_area) / len(label)

    return power_correct, area_correct
