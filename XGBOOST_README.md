# XGBoost Implementation for ELEC-5140 Project

This document describes the XGBoost regression implementation added to the ELEC-5140 VLSI design optimization project.

## Overview

The XGBoost implementation provides an alternative machine learning approach to predict power consumption and chip area from microarchitecture embeddings. Unlike the existing PyTorch-based neural networks (MLP, UNet, Transformer), XGBoost uses gradient boosting decision trees.

## Features

- **Dual-target regression**: Separate XGBoost models for power and area prediction
- **PyTorch compatibility**: Wrapper class that integrates seamlessly with existing framework
- **Hyperparameter control**: Comprehensive command-line arguments for XGBoost tuning
- **Validation support**: Built-in train/validation split for model evaluation
- **Visualization**: Custom plotting functions for XGBoost results

## Files Added/Modified

### New Files
- `xgboost_utils.py` - Core XGBoost implementation and utilities
- `test_xgboost.py` - Test script for XGBoost functionality
- `XGBOOST_README.md` - This documentation

### Modified Files
- `arguments.py` - Added XGBoost-specific command-line arguments
- `main.py` - Added XGBoost model selection and training logic
- `plotting.py` - Added XGBoost-specific plotting functions

## Usage

### Basic Usage
```bash
python main.py --model-name xgboost
```

### With Custom Hyperparameters
```bash
python main.py --model-name xgboost \
    --xgb-n-estimators 100 \
    --xgb-max-depth 8 \
    --xgb-learning-rate 0.1 \
    --xgb-reg-alpha 0.1 \
    --xgb-reg-lambda 1.0
```

### Available XGBoost Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| `--xgb-n-estimators` | 100 | Number of boosting rounds |
| `--xgb-max-depth` | 6 | Maximum depth of trees |
| `--xgb-learning-rate` | 0.1 | Learning rate (eta) |
| `--xgb-subsample` | 1.0 | Subsample ratio of training instances |
| `--xgb-colsample-bytree` | 1.0 | Subsample ratio of columns |
| `--xgb-reg-alpha` | 0.0 | L1 regularization term |
| `--xgb-reg-lambda` | 1.0 | L2 regularization term |

## Implementation Details

### Architecture
The XGBoost implementation uses two separate models:
1. **Power Model**: Predicts normalized power consumption
2. **Area Model**: Predicts normalized chip area

### Data Processing
- Uses the same data preprocessing as PyTorch models
- Converts PyTorch DataLoaders to numpy arrays
- Maintains k-fold cross-validation compatibility

### Training Process
1. Load and preprocess data using existing pipeline
2. Split into train/test sets based on k-fold parameters
3. Train separate XGBoost models for power and area
4. Evaluate on test set using same metrics as other models

### Output Compatibility
- Generates same CSV results format as other models
- Creates visualization plots (bar charts instead of line plots)
- Maintains compatibility with existing evaluation metrics

## Performance Characteristics

### Advantages
- **Fast training**: No gradient descent iterations
- **Feature importance**: Built-in feature importance analysis
- **Robust**: Less prone to overfitting with proper regularization
- **No GPU required**: Runs efficiently on CPU

### Considerations
- **Single training phase**: No epoch-based training like neural networks
- **Memory efficient**: Lower memory footprint than deep learning models
- **Interpretable**: Tree-based models are more interpretable

## Example Results

After running XGBoost, you'll find:

1. **Results CSV** (`./res/xgboost-{fold}.csv`):
   ```
   train losses,0.391
   test losses,0.375
   power acc,0.003
   area acc,0.012
   ```

2. **Loss Plot** (`./plots/losses_plot_xgboost_{fold}.png`):
   - Bar chart showing final train/test losses

3. **Accuracy Plot** (`./plots/acc_plot_xgboost_{fold}.png`):
   - Bar chart showing power and area prediction accuracy

## Testing

Run the test script to verify XGBoost functionality:

```bash
python test_xgboost.py
```

This will test:
- Default XGBoost parameters
- Custom hyperparameter configurations
- Different k-fold settings
- Comparison with existing models

## Integration with Existing Framework

The XGBoost implementation is designed to be a drop-in replacement for existing models:

```python
# Same interface as PyTorch models
if args.model_name == 'xgboost':
    model = create_xgboost_model(args)
    # Training and evaluation handled automatically
```

## Dependencies

- `xgboost >= 2.0.0`
- `scikit-learn` (for metrics)
- `numpy`
- `pandas`
- `matplotlib` (for plotting)

## Future Enhancements

Potential improvements for the XGBoost implementation:

1. **Feature importance analysis**: Add feature importance visualization
2. **Hyperparameter tuning**: Integrate with optuna or similar for automated tuning
3. **Early stopping**: Add early stopping based on validation performance
4. **Cross-validation**: Built-in cross-validation for hyperparameter selection
5. **Ensemble methods**: Combine XGBoost with neural network predictions
