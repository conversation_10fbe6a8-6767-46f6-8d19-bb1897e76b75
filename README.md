# ELEC-5140 Project

## Overview

This project implements machine learning models to predict power consumption and chip area from microarchitecture embeddings for VLSI design optimization.

## Supported Models

- **MLP**: Multi-Layer Perceptron (feedforward neural network)
- **UNet**: U-shaped convolutional neural network
- **Transformer**: Transformer model with attention mechanisms
- **XGBoost**: Gradient boosting decision trees (NEW!)

## Usage

### Basic Usage
```bash
python3 main.py --model-name [mlp|unet|transformer|xgboost]
```

### XGBoost Examples
```bash
# Basic XGBoost
python3 main.py --model-name xgboost

# XGBoost with custom parameters
python3 main.py --model-name xgboost \
    --xgb-n-estimators 100 \
    --xgb-max-depth 8 \
    --xgb-learning-rate 0.1
```

### Neural Network Examples
```bash
# MLP with custom parameters
python3 main.py --model-name mlp --epochs 50 --lr 1e-4

# UNet with different embedding dimension
python3 main.py --model-name unet --embed-dim 128
```

## XGBoost Implementation

The XGBoost implementation provides an alternative approach using gradient boosting trees. See `XGBOOST_README.md` for detailed documentation.

### Key Features:
- Dual-target regression for power and area prediction
- Comprehensive hyperparameter control
- Fast training without GPU requirements
- Built-in feature importance analysis

## Stated Results

They are stored on github, the link is https://github.com/ranyangit/elec5140-project/tree/main. Remember to switch to `with-res` branch