import torch 
import torch.nn as nn
from arguments import get_args
import math

class MLP(nn.Module):
    def __init__(self, num_embeddings=147, embed_dim=128, out_dim=2) -> None:
        super().__init__()
        self.s = embed_dim      # seq len
        self.f = 29             # feature num

        self.activation = nn.ReLU()

        args = get_args()

        self.embedding_table = nn.Embedding(num_embeddings=num_embeddings, embedding_dim=embed_dim)
        # self.lstm = nn.LSTM(input_size=self.f, hidden_size=self.s, num_layers=args.num_layers, batch_first=True)
        # self.conv = nn.Conv1d(self.f, self.f // 2, kernel_size=3, padding=1)
        # self.conv2 = nn.Conv1d(self.f // 2, self.f //, kernel_size=3, padding=1)
        self.linear_relu_stack = nn.Sequential(
            nn.Linear(self.f * embed_dim, embed_dim),
            # nn.Linear(self.f * 15, embed_dim),
            self.activation,
           
            nn.Linear(embed_dim, embed_dim), 
            self.activation,

            nn.Linear(embed_dim, out_dim), 
        )



    def _reshape_bsf(self, tensor):
        return tensor.reshape(-1, self.s, self.f)

    def _reshape_bfs(self, tensor):
        return tensor.reshape(-1, self.f, self.s)
    
    def forward(self, embedding_id: torch.Tensor):
        embedding: torch.Tensor = self.embedding_table(embedding_id.long())
        embedding = embedding.flatten(start_dim=1, end_dim=-1)
        return self.linear_relu_stack(embedding)


def double_conv(in_channels, out_channels):
    return nn.Sequential(
        nn.Conv1d(in_channels, out_channels, 3, padding=1),
        nn.ReLU(inplace=True),
        nn.Conv1d(out_channels, out_channels, 3, padding=1),
        nn.ReLU(inplace=True)
    )   

class UNet(nn.Module):

    def __init__(self, n_class=2, num_embeddings=147, embed_dim=64):
        super().__init__()

        self.embed_dim = embed_dim
        self.inter_dim = int(math.sqrt(embed_dim))

        self.embedding_table = nn.Embedding(num_embeddings=num_embeddings, embedding_dim=embed_dim)
        self.dconv_down1 = double_conv(29, 64)
        self.dconv_down2 = double_conv(64, 128)
        self.dconv_down3 = double_conv(128, 256)
        self.dconv_down4 = double_conv(256, 512)        

        self.maxpool = nn.MaxPool1d(2)
        self.upsample = nn.Upsample(scale_factor=2, mode='linear', align_corners=True)        
        
        self.dconv_up3 = double_conv(256 + 512, 256)
        self.dconv_up2 = double_conv(128 + 256, 128)
        self.dconv_up1 = double_conv(128 + 64, 64)

        self.fc1 = nn.Linear(in_features=64 * self.embed_dim, out_features=self.embed_dim)
        self.fc2 = nn.Linear(in_features=self.embed_dim, out_features=n_class)

    def forward(self, x):
        x = self.embedding_table(x.long())
        
        conv1 = self.dconv_down1(x)
        x = self.maxpool(conv1)

        conv2 = self.dconv_down2(x)
        x = self.maxpool(conv2)

        conv3 = self.dconv_down3(x)
        x = self.maxpool(conv3)   

        x = self.dconv_down4(x)

        x = self.upsample(x)        
        x = torch.cat([x, conv3], dim=1)

        x = self.dconv_up3(x)
        x = self.upsample(x)        
        x = torch.cat([x, conv2], dim=1)       

        x = self.dconv_up2(x)
        x = self.upsample(x)        
        x = torch.cat([x, conv1], dim=1)   

        x = self.dconv_up1(x)

        out = self.fc1(x.flatten(start_dim=1, end_dim=-1))
        out = self.fc2(nn.ReLU()(out))
        # print(x.size())
        # exit(0)
        # out = self.conv_last(x)
        # print(out.size())
        # exit(0)
        return out


class TransformerModel(nn.Module):
    def __init__(self, n_class=2, hidden_size=64, nhead=4, num_layers=4, num_embeddings=147):
        super(TransformerModel, self).__init__()
        self.feature_num = 29
        self.hidden_size = hidden_size
        self.transformer = nn.Transformer(hidden_size, nhead, num_layers, batch_first=True)
        self.fc = nn.Linear(hidden_size * self.feature_num, n_class)

        self.embedding_table = nn.Embedding(num_embeddings=num_embeddings, embedding_dim=hidden_size)

    def forward(self, x):
        x = self.embedding_table(x.long())
        x = self.transformer(x, x)
        out = self.fc(x.flatten(start_dim=1, end_dim=-1))
        return out