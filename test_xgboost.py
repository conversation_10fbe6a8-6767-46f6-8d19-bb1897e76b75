#!/usr/bin/env python3
"""
Test script for XGBoost implementation in ELEC-5140 project.
This script demonstrates how to use the XGBoost model with different hyperparameters.
"""

import subprocess
import sys
import os

def run_command(cmd):
    """Run a command and print the output"""
    print(f"\n{'='*60}")
    print(f"Running: {cmd}")
    print('='*60)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=120)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("Command timed out!")
        return False
    except Exception as e:
        print(f"Error running command: {e}")
        return False

def main():
    """Test different XGBoost configurations"""
    
    print("Testing XGBoost Implementation for ELEC-5140 Project")
    print("="*60)
    
    # Test 1: Basic XGBoost with default parameters
    print("\n1. Testing XGBoost with default parameters...")
    success1 = run_command("python main.py --model-name xgboost --xgb-n-estimators 10")
    
    # Test 2: XGBoost with custom hyperparameters
    print("\n2. Testing XGBoost with custom hyperparameters...")
    success2 = run_command(
        "python main.py --model-name xgboost "
        "--xgb-n-estimators 20 "
        "--xgb-max-depth 8 "
        "--xgb-learning-rate 0.05 "
        "--xgb-reg-alpha 0.1 "
        "--xgb-reg-lambda 1.5"
    )
    
    # Test 3: XGBoost with different k-fold
    print("\n3. Testing XGBoost with different k-fold...")
    success3 = run_command(
        "python main.py --model-name xgboost "
        "--xgb-n-estimators 15 "
        "--test-fold 1 "
        "--kfold 5"
    )
    
    # Test 4: Compare with MLP (quick test)
    print("\n4. Quick comparison with MLP...")
    success4 = run_command(
        "python main.py --model-name mlp "
        "--epochs 5 "
        "--test-fold 3"
    )
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"1. XGBoost default: {'✓ PASSED' if success1 else '✗ FAILED'}")
    print(f"2. XGBoost custom:  {'✓ PASSED' if success2 else '✗ FAILED'}")
    print(f"3. XGBoost k-fold:  {'✓ PASSED' if success3 else '✗ FAILED'}")
    print(f"4. MLP comparison:  {'✓ PASSED' if success4 else '✗ FAILED'}")
    
    # Check generated files
    print("\nGenerated files:")
    if os.path.exists("./plots/losses_plot_xgboost_3.png"):
        print("✓ XGBoost loss plot generated")
    if os.path.exists("./plots/acc_plot_xgboost_3.png"):
        print("✓ XGBoost accuracy plot generated")
    if os.path.exists("./res/xgboost-3.csv"):
        print("✓ XGBoost results CSV generated")
    
    all_passed = success1 and success2 and success3 and success4
    print(f"\nOverall: {'✓ ALL TESTS PASSED' if all_passed else '✗ SOME TESTS FAILED'}")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
